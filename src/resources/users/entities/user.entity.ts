import { Assessment } from 'src/resources/assessment-forms/entities/assessment.entity';
import { Submission } from 'src/resources/assessment-forms/entities/submission.entity';
import { Program } from 'src/resources/programs/entities/program.entity';
import { Role } from 'src/resources/roles/entities/role.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserSkill } from 'src/resources/individual-develop-plans/skills/entities/user-skill.entity';
import { PermAuditSkill } from 'src/resources/individual-develop-plans/skills/entities/perm-audit-skill.entity';
import { Skill } from 'src/resources/individual-develop-plans/skills/entities/skill.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';

import { TypePlan } from 'src/resources/individual-develop-plans/type-plans/entities/type-plan.entity';
import { DepartmentUsers } from 'src/resources/departments/entity/departments-users.entity';

@Entity('HRD_USERS')
export class User {
  @PrimaryGeneratedColumn({ name: 'USR_ID' })
  id: number;

  @Column({ name: 'USR_NAME', length: 100 })
  name: string;

  @Column({ name: 'USR_EMAIL', unique: true, length: 100 })
  email: string;

  @Column({ name: 'USR_PASSWORD' })
  @Exclude() // Exclude password from serialization
  password: string;

  @ManyToMany(() => Role, (role) => role.users, { eager: true })
  @JoinTable({
    name: 'HRD_USER_HAS_ROLES',
    joinColumn: { name: 'USR_ID', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'ROL_ID', referencedColumnName: 'id' },
  })
  roles: Role[];

  @OneToMany(() => Program, (program) => program.creator)
  programs: Program[];

  @OneToMany(() => Assessment, (assessment) => assessment.creator)
  assessments: Assessment[];

  @OneToMany(() => Submission, (submission) => submission.user)
  submissions: Submission[];

  @OneToMany(() => UserSkill, (us) => us.user, {
    cascade: true,
  })
  userSkills: UserSkill[];

  @OneToMany(() => PermAuditSkill, (pa) => pa.user)
  permAuditSkills: PermAuditSkill[];

  @OneToMany(() => Skill, (s) => s.evaluator)
  evaluatedSkills: Skill[];

  @OneToMany(() => CareerRecords, (cr) => cr.user)
  careerRecords: CareerRecords[];

  @OneToMany(() => DepartmentUsers, (du) => du.users)
  departmentUsers: DepartmentUsers[];

  @OneToMany(() => TypePlan, (typePlan) => typePlan.privatePlanUser, {
    nullable: true,
  })
  privatePlans: TypePlan[];
}
