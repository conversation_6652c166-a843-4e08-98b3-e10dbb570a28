import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Role } from 'src/resources/roles/entities/role.entity';
import { DepartmentUsers } from './departments-users.entity';

@Entity('HRD_DEPARTMENT_USER_ROLES')
export class DepartmentUserRoles {
  @PrimaryGeneratedColumn({ name: 'DUR_ID' })
  id: number;

  @Column({ name: 'DTU_ID', nullable: false })
  departmentUserId: number;

  @Column({ name: 'DUR_ROLE_ID', nullable: false })
  roleId: number;

  @ManyToOne(() => DepartmentUsers, (du) => du.departmentUserRoles)
  @JoinColumn({ name: 'DTU_ID' })
  departmentUser: DepartmentUsers;

  @ManyToOne(() => Role, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'DUR_ROLE_ID' })
  role: Role;
}
