import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Department } from './departments.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { DepartmentUserRoles } from './department-user-roles.entity';

@Entity('HRD_DEPARTMENTS_USERS')
export class DepartmentUsers {
  @PrimaryGeneratedColumn({ name: 'DTU_ID' })
  id: number;

  @Column({ name: 'DEPT_ID', nullable: false })
  departmentId: number;

  @Column({ name: 'DTU_USER_ID', nullable: false })
  userId: number;

  @ManyToOne(() => Department, (department) => department.departmentUsers)
  @JoinColumn({ name: 'DEPT_ID' })
  department: Department;

  @ManyToOne(() => User, (user) => user.departmentUsers)
  @JoinColumn({ name: 'DTU_USER_ID' })
  users: User;

  @OneToMany(
    () => DepartmentUserRole<PERSON>,
    (departmentRoles) => departmentRoles.departmentUserId,
  )
  departmentUserRoles: DepartmentUserRoles[];

  // One to many department user role
}
