import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { DevelopmentPlan } from 'src/resources/individual-develop-plans/development-plans/entities/development-plan.entity';
import { DepartmentUsers } from './departments-users.entity';

@Entity('HRD_DEPARTMENTS')
export class Department {
  @PrimaryGeneratedColumn({ name: 'DEPT_ID' })
  id: number;

  @Column({ name: 'DEPT_NAME', nullable: false })
  name: string;

  @OneToMany(() => DepartmentUsers, (deptUser) => deptUser.department, {
    cascade: true,
  })
  departmentUsers: DepartmentUsers[];

  @OneToMany(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.department,
  )
  developmentPlans: DevelopmentPlan[];
}
