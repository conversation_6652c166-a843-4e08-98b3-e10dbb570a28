import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';

@Controller('departments')
export class DepartmentsController {
  @Get()
  findAll() {
    // Return all departments
    return [];
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    // Return a single department by id
    return { id };
  }

  @Post()
  create(@Body() createDepartmentDto: any) {
    // Create a new department
    return createDepartmentDto;
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateDepartmentDto: any) {
    // Update a department by id
    return { id, ...updateDepartmentDto };
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    // Remove a department by id
    return { deleted: id };
  }
}