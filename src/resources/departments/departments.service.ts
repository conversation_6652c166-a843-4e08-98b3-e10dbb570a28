import { Injectable } from '@nestjs/common';

@Injectable()
export class DepartmentsService {
  findAll(): string[] {
    return ['Department 1', 'Department 2', 'Department 3'];
  }

  findOne(id: number): string {
    return `Department ${id}`;
  }

  create(department: string): string {
    return `Created department: ${department}`;
  }

  update(id: number, department: string): string {
    return `Updated department ${id} to ${department}`;
  }

  remove(id: number): string {
    return `Removed department ${id}`;
  }
}