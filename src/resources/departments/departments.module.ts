import { Module } from '@nestjs/common';
import { DepartmentsService } from './departments.service';
import { DepartmentsController } from './departments.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Department } from './entity/departments.entity';
import { DepartmentUserRoles } from './entity/department-user-roles.entity';
import { DepartmentUsers } from './entity/departments-users.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Department, DepartmentUsers, DepartmentUserRoles]),
  ], // Assuming Department is the entity for departments
  providers: [DepartmentsService],
  controllers: [DepartmentsController],
  exports: [DepartmentsService],
})
export class DepartmentsModule {}
