import { Role } from 'src/resources/roles/entities/role.entity';
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
} from 'typeorm';

@Entity('HRD_PERMISSIONS')
export class Permission {
  @PrimaryGeneratedColumn({ name: 'PER_ID' })
  id: number;

  @Column({ name: 'PER_NAME' })
  name: string;

  @Column({ name: 'PER_NAME_EN' })
  nameEn: string;

  @Column({ name: 'PER_STATUS', default: true })
  status: boolean;

  @Column({ name: 'PER_IS_DEFAULT', default: false })
  isDefault: boolean;

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];
}
