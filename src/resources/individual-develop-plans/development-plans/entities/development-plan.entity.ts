import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';
import { AgeWorkCriteria } from '../../age-work/entities/age-work-criteria.entity';
import { Department } from 'src/resources/departments/entity/departments.entity';

@Entity('IDP_DEVELOPMENT_PLANS')
export class DevelopmentPlan {
  @PrimaryGeneratedColumn({ name: 'DP_ID' })
  id: number;

  @Column({ name: 'DP_NAME' })
  name: string;

  @Column({ name: 'DP_DESCRIPTION' })
  description: string;

  @Column({ name: 'DP_IS_ACTIVE', default: false })
  isActive: boolean;

  @Column({ name: 'DP_PARENT_ID', nullable: true })
  parentId: number;

  @Column({ name: 'DEPT_ID', nullable: true })
  departmentId: number;

  @CreateDateColumn({
    name: 'DP_CREATED_AT',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'DP_UPDATED_AT',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  // --- Self-referencing fields ---

  // Many-to-One relationship to itself (parent DevelopmentPlan)
  // A child DevelopmentPlan has one parent (or none, if it's a top-level plan)
  @ManyToOne(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.children,
    {
      nullable: true, // A top-level plan has no parent
      onDelete: 'SET NULL', // If a parent is deleted, set children's parentId to NULL
    },
  )
  @JoinColumn({ name: 'DP_PARENT_ID' }) // This creates the 'DP_PARENT_ID' foreign key column
  parent: DevelopmentPlan;

  // One-to-Many relationship to itself (child DevelopmentPlans)
  // A parent DevelopmentPlan can have many children
  @OneToMany(() => DevelopmentPlan, (developmentPlan) => developmentPlan.parent)
  children: DevelopmentPlan[];

  // ageWork criteria
  @ManyToOne(
    () => AgeWorkCriteria,
    (ageWorkCriteria) => ageWorkCriteria.developmentPlans,
  )
  @JoinColumn({ name: 'AGE_WORK_CRITERIA_ID' })
  ageWorkCriteria: AgeWorkCriteria;

  @OneToMany(() => TypePlan, (typePlan) => typePlan.developmentPlan, {
    eager: true,
    cascade: true,
  })
  typePlans: TypePlan[];

  @ManyToOne(() => Department, (department) => department.developmentPlans)
  @JoinColumn({ name: 'DEPT_ID' })
  department: Department;
}
