import { Injectable } from '@nestjs/common';
import { CreateDevelopmentPlanDto } from './dto/create-development-plan.dto';
import { UpdateDevelopmentPlanDto } from './dto/update-development-plan.dto';
import { DevelopmentPlansQueryDto } from './dto/development-plans-query.dto';
import type { DevelopmentPlanParams } from 'src/types/params';
import {
  type EntityManager,
  Like,
  FindManyOptions,
  FindOptionsWhere,
  FindOptionsOrder,
  IsNull,
  Not,
} from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import { DevelopmentPlan } from './entities/development-plan.entity';
import { Department } from 'src/resources/departments/entity/departments.entity';

@Injectable()
export class DevelopmentPlansService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
  ) {}

  async create(createDevelopmentPlanDto: CreateDevelopmentPlanDto) {
    const ent = this.entityManager.create(
      DevelopmentPlan,
      createDevelopmentPlanDto,
    );

    const devplan = await this.entityManager.save(ent);

    // create plan for every department
    const repoDep = this.entityManager.getRepository(Department);
    const departments = await repoDep.find({ select: { id: true } });
    for (const department of departments) {
      const newDevPlan = this.entityManager.create(DevelopmentPlan, {
        ...createDevelopmentPlanDto,
        department: department,
        parent: devplan,
      });
      await this.entityManager.save(newDevPlan);
    }

    return devplan;
  }

  async findAll(
    query: DevelopmentPlansQueryDto,
  ): Promise<[DevelopmentPlan[], number]> {
    const repoDev = this.entityManager.getRepository(DevelopmentPlan);

    const whereConditions: FindOptionsWhere<DevelopmentPlan> = {};
    const orConditions: FindOptionsWhere<DevelopmentPlan>[] = [];

    // Handle search
    if (query.search) {
      orConditions.push(
        { name: Like(`%${query.search}%`) },
        { description: Like(`%${query.search}%`) },
      );
    }

    // Handle isCentral filter (root nodes)
    if (query.isCentral === 'central') {
      whereConditions.parentId = IsNull();
    } else if (query.isCentral === 'department') {
      // get only leaf node
      whereConditions.parentId = Not(IsNull());
    }

    // Handle parentId filter
    if (query.parentId) {
      whereConditions.parentId = query.parentId;
    }

    // Combine conditions
    let where:
      | FindOptionsWhere<DevelopmentPlan>
      | FindOptionsWhere<DevelopmentPlan>[] = whereConditions;

    // If there are OR conditions, combine them with AND with the whereConditions
    if (orConditions.length > 0) {
      where = [
        ...orConditions.map((cond) => ({
          ...cond,
          ...(Object.keys(whereConditions).length > 0 ? whereConditions : {}),
        })),
      ];
    }

    const findOptions: FindManyOptions<DevelopmentPlan> = {
      where: where,
      order: {
        [query.sortBy]: query.order,
      } as FindOptionsOrder<DevelopmentPlan>,
      take: query.limit,
      skip: (query.page - 1) * query.limit,
    };

    return repoDev.findAndCount(findOptions);
  }

  async findOne(
    devId: number,
    query?: Partial<DevelopmentPlanParams>,
  ): Promise<DevelopmentPlan> {
    // Provide default values for partial query
    const defaultQuery: DevelopmentPlanParams = {
      sortBy: null,
      order: 'ASC',
      limit: 10,
      page: 1,
      search: null,
      type: 'ทั้งหมด',
    };

    const finalQuery = { ...defaultQuery, ...query };

    const repoDev = this.entityManager.getRepository(DevelopmentPlan);

    // For finding a specific development plan by ID with search
    let whereConditions:
      | FindOptionsWhere<DevelopmentPlan>
      | FindOptionsWhere<DevelopmentPlan>[];

    if (finalQuery.search) {
      // Search within the specific dev plan
      whereConditions = [
        {
          id: devId,
          name: Like(`%${finalQuery.search}%`),
        },
        {
          id: devId,
          description: Like(`%${finalQuery.search}%`),
        },
      ];
    } else {
      // Just find by ID
      whereConditions = {
        id: devId,
      };
    }

    const findOptions: FindManyOptions<DevelopmentPlan> = {
      where: whereConditions,
      order: {} as FindOptionsOrder<DevelopmentPlan>,
      take: finalQuery.limit,
      skip: (finalQuery.page - 1) * finalQuery.limit,
      relations: {
        typePlans: {
          ageWork: true,
          privatePlanUser: true,
        },
      },
    };

    if (finalQuery.sortBy) {
      const validSortFields: (keyof DevelopmentPlan)[] = [
        'id',
        'name',
        'description',
        'isActive',
      ];
      if (
        validSortFields.includes(finalQuery.sortBy as keyof DevelopmentPlan)
      ) {
        const sortField = finalQuery.sortBy as keyof DevelopmentPlan;
        findOptions.order = {
          [sortField]: finalQuery.order,
        } as FindOptionsOrder<DevelopmentPlan>;
      }
    } else {
      findOptions.order = { id: finalQuery.order };
    }

    const [data] = await repoDev.findAndCount(findOptions);
    return data[0];
  }

  update(id: number, updateDevelopmentPlanDto: UpdateDevelopmentPlanDto) {
    return this.entityManager.update(
      DevelopmentPlan,
      { id },
      updateDevelopmentPlanDto,
    );
  }

  remove(id: number) {
    return this.entityManager.delete(DevelopmentPlan, { id });
  }
}
