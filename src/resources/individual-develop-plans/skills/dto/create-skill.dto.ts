// src/skills/dto/create-skill.dto.ts

import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsInt,
  IsArray,
  IsBoolean,
} from 'class-validator';

export class CreateSkillDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum([
    'ทั่วไปบุคลากร',
    'ทั่วไปผู้บริหาร',
    'เฉพาะด้านสายวิชาการ',
    'เฉพาะด้านสายสนับสนุนวิชาการ',
    'เฉพาะด้านผู้บริหาร',
  ])
  career_type:
    | 'ทั่วไปบุคลากร'
    | 'ทั่วไปผู้บริหาร'
    | 'เฉพาะด้านสายวิชาการ'
    | 'เฉพาะด้านสายสนับสนุนวิชาการ'
    | 'เฉพาะด้านผู้บริหาร';

  @IsInt()
  programId: number;

  @IsBoolean()
  tracking: boolean;

  @IsOptional()
  @IsInt()
  evaluatorId: number; // userId ของผู้ประเมิน

  @IsOptional()
  @IsInt()
  dep_id: number; // ส่วนงาน

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  competencyIds?: number[];
}
