import { Program } from 'src/resources/programs/entities/program.entity';
import { User } from 'src/resources/users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { UserSkill } from './user-skill.entity';
import { PermAuditSkill } from './perm-audit-skill.entity';
import { CompetencySkill } from './competency-skill.entity';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';

@Entity('IDP_SKILLS')
export class Skill {
  @PrimaryGeneratedColumn({ name: 'IDS_ID' })
  id: number;

  @Column({ name: 'IDS_NAME' })
  name: string;

  @Column({ name: 'IDS_DESCRIPTION', nullable: true })
  description: string;

  @Column({ name: 'IDS_TRACKING', nullable: true })
  tracking: boolean;

  @Column({
    name: 'IDS_CAREER_TYPE',
    type: 'enum',
    enum: [
      'ทั่วไปบุคลากร',
      'ทั่วไปผู้บริหาร',
      'เฉพาะด้านสายวิชาการ',
      'เฉพาะด้านสายสนับสนุนวิชาการ',
      'เฉพาะด้านผู้บริหาร',
    ],
    nullable: true,
  })
  career_type:
    | 'ทั่วไปบุคลากร'
    | 'ทั่วไปผู้บริหาร'
    | 'เฉพาะด้านสายวิชาการ'
    | 'เฉพาะด้านสายสนับสนุนวิชาการ'
    | 'เฉพาะด้านผู้บริหาร';

  @Column({ name: 'IDS_PROGRAM_ID' })
  programId: number;

  @Column({ name: 'IDS_EVALUATOR_ID', nullable: true })
  evaluatorId: number;

  @Column({ name: 'IDS_DEP_ID', nullable: true })
  dep_id?: number;

  @ManyToOne(() => Program, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'IDS_PROGRAM_ID' })
  program: Program;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'IDS_EVALUATOR_ID' })
  evaluator: User;

  @OneToMany(() => UserSkill, (us) => us.skill)
  userSkills: UserSkill[];

  @OneToMany(() => PermAuditSkill, (pa) => pa.skill)
  permAuditSkills: PermAuditSkill[];

  @OneToMany(() => CompetencySkill, (cs) => cs.skill)
  competencySkills: CompetencySkill[];
}
