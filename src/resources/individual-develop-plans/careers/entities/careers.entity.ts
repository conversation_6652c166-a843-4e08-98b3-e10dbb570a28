import { Role } from 'src/resources/roles/entities/role.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinC<PERSON><PERSON><PERSON>,
} from 'typeorm';
import { CareerRecords } from '../../career-records/entites/career-records.entity';

@Entity('IDP_CAREERS')
export class Career {
  @PrimaryGeneratedColumn({ name: 'CR_ID' })
  id: number;

  @Column({ name: 'CR_CAREER_TYPE' })
  career_type: string;

  @Column({ name: 'CR_CAREER_NAME' })
  career_name: string;

  @Column({ name: 'CR_CAREER_RANK' })
  career_rank: string;

  @JoinColumn({ name: 'CR_CRR_ID' })
  @OneToMany(() => CareerRecords, (cr) => cr.career_Id)
  careerRecords: CareerRecords[];
}
