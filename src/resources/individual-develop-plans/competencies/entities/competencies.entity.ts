import { CompetencySkill } from 'src/resources/individual-develop-plans/skills/entities/competency-skill.entity';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';

export enum CompType {
  CORE = 'สมรรถนะหลัก',
  FUNCTIONAL = 'ประจำสายงาน',
  MANAGERIAL = 'ทางการบริหาร',
}

export enum CareerType {
  ACADEMIC = 'วิชาการ',
  SUPPORT = 'สนับสนุน',
}

@Entity('IDP_COMPETENCIES')
export class Competency {
  @PrimaryGeneratedColumn({ name: 'CPT_ID' })
  id: number;

  @Column({
    name: 'CPT_COMP_TYPE',
    type: 'enum',
    enum: CompType,
  })
  comp_type: CompType;

  @Column({
    name: 'CPT_CAREER_TYPE',
    type: 'enum',
    enum: CareerType,
    nullable: true,
  })
  career_type: CareerType;

  @Column({ name: 'CPT_NAME' })
  name: string;

  @Column({ name: 'CPT_DESCRIPTION', nullable: true })
  description: string;

  @OneToMany(() => CompetencySkill, (cs) => cs.competency)
  competencySkills: CompetencySkill[];
}
