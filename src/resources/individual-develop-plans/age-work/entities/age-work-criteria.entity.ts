import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>umn, OneToMany } from 'typeorm';
import { AgeWork } from './age-work.entity';
import { DevelopmentPlan } from '../../development-plans/entities/development-plan.entity';

@Entity('IDP_AGE_WORK_CRITERIA')
export class AgeWorkCriteria {
  @PrimaryGeneratedColumn({ name: 'ID' })
  id: number;

  @Column({ name: 'NAME', type: 'varchar', length: 255 })
  name: string;

  @OneToMany(() => AgeWork, (ageWork) => ageWork.ageWorkCriteria)
  ageWorks: AgeWork[];

  // development plan
  @OneToMany(() => DevelopmentPlan, (developmentPlan) => developmentPlan.ageWorkCriteria)
  developmentPlans: DevelopmentPlan[];
}
