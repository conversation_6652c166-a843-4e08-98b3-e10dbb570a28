import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'typeorm';
import { TypePlan } from './type-plan.entity';
import { Skill } from '../../skills/entities/skill.entity';
import { Career } from '../../careers/entities/careers.entity';

@Entity('IDP_CAREER_SKILL_PLANS')
export class CareerSkillPlan {
  @PrimaryGeneratedColumn({ name: 'CSP_ID' })
  id: number;

  @ManyToOne(() => TypePlan, (typePlan) => typePlan.careerSkillPlans, {
    nullable: false,
  })
  @JoinColumn({ name: 'TP_ID' })
  typePlan: TypePlan;

  @ManyToOne(() => Skill, { nullable: false })
  @JoinColumn({ name: 'SKILL_ID' })
  skill: Skill;

  @ManyToOne(() => Career, { nullable: true })
  @JoinColumn({ name: 'CAREER_ID' })
  career: Career | null;
}
