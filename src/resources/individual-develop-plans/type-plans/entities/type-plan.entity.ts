import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
} from 'typeorm';
import { DevelopmentPlan } from '../../development-plans/entities/development-plan.entity';
import { CareerSkillPlan } from './carreer-skill-plan.entity';
import { AgeWork } from '../../age-work/entities/age-work.entity';
import { User } from 'src/resources/users/entities/user.entity';

export enum PlanType {
  GENERAL_STAFF = 'ทั่วไปบุคลากร',
  GENERAL_MANAGER = 'ทั่วไปผู้บริหาร',
  SPECIFIC_MANAGEMENT = 'เฉพาะด้านบริหาร',
  SPECIFIC_ACADEMIC = 'เฉพาะด้านวิชาการ',
  SPECIFIC_SUPPORT = 'เฉพาะสายสนับสนุน',
  POSITION = 'ตำแหน่ง',
}

@Entity('IDP_TYPE_PLANS')
export class TypePlan {
  @PrimaryGeneratedColumn({ name: 'TP_ID' })
  id: number;

  @Column({ name: 'FAC_ID', length: 10, nullable: true })
  facId: string | null;

  @Column({ name: 'TP_PRIVATE_USER_ID', nullable: true })
  privateUserId: number | null;

  @Column({ name: 'TP_AGE_WORK_CRITERIA_ID', nullable: false })
  ageWorkCriteriaId: number;

  @Column({
    name: 'TP_NAME',
    type: 'enum',
    enum: PlanType,
    nullable: false,
  })
  name: PlanType;

  @ManyToOne(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.typePlans,
    { nullable: false },
  )
  @JoinColumn({ name: 'DP_ID' })
  developmentPlan: DevelopmentPlan;

  // One-to-Many relationship with CareerSkillPlan
  @OneToMany(
    () => CareerSkillPlan,
    (careerSkillPlan) => careerSkillPlan.typePlan,
  )
  careerSkillPlans: CareerSkillPlan[];

  // age work entity not age work criteria
  @ManyToOne(() => AgeWork, (ageWork) => ageWork.typePlans)
  @JoinColumn({ name: 'AGE_WORK_ID' })
  ageWork: AgeWork;

  @ManyToOne(() => User, (user) => user.privatePlans, { nullable: true })
  @JoinColumn({ name: 'PRIVATE_USER_ID' })
  privatePlanUser: User;
}
