import { Injectable } from '@nestjs/common';
import { CreateTypePlanDto } from './dto/create-type-plan.dto';
import { UpdateTypePlanDto } from './dto/update-type-plan.dto';

@Injectable()
export class TypePlansService {
  create(createTypePlanDto: CreateTypePlanDto) {
    return 'This action adds a new typePlan';
  }

  findAll() {
    return `This action returns all typePlans`;
  }

  findOne(id: number) {
    return `This action returns a #${id} typePlan`;
  }

  update(id: number, updateTypePlanDto: UpdateTypePlanDto) {
    return `This action updates a #${id} typePlan`;
  }

  remove(id: number) {
    return `This action removes a #${id} typePlan`;
  }
}
