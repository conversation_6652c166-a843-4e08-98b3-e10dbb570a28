import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { TypePlansService } from './type-plans.service';
import { CreateTypePlanDto } from './dto/create-type-plan.dto';
import { UpdateTypePlanDto } from './dto/update-type-plan.dto';

@Controller('type-plans')
export class TypePlansController {
  constructor(private readonly typePlansService: TypePlansService) {}

  @Post()
  create(@Body() createTypePlanDto: CreateTypePlanDto) {
    return this.typePlansService.create(createTypePlanDto);
  }

  @Get()
  findAll() {
    return this.typePlansService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.typePlansService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTypePlanDto: UpdateTypePlanDto) {
    return this.typePlansService.update(+id, updateTypePlanDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.typePlansService.remove(+id);
  }
}
