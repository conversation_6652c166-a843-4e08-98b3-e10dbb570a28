import { DepartmentUserRoles } from 'src/resources/departments/entity/department-user-roles.entity';
import { Permission } from 'src/resources/permissions/entities/permission.entity';
import { User } from 'src/resources/users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  JoinTable,
  OneToMany,
  JoinColumn,
} from 'typeorm';

@Entity('HRD_ROLES')
export class Role {
  @PrimaryGeneratedColumn({ name: 'ROL_ID' })
  id: number;

  @Column({ name: 'ROL_NAME', unique: true, nullable: false })
  name: string;

  @Column({ name: 'ROL_DESCRIPTION', nullable: true })
  description: string;

  @ManyToMany(() => User, (user) => user.roles)
  users: User[];

  @ManyToMany(() => Permission, (permission) => permission.roles)
  @JoinTable({
    name: 'HRD_ROLES_HAS_PERMISSIONS',
    joinColumn: { name: 'ROL_ID', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'PER_ID', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  @OneToMany(
    () => DepartmentUserRoles,
    (departmentRoles) => departmentRoles.role,
    {
      cascade: true,
    },
  )
  @JoinColumn({ name: 'DEPT_ROLE_ID' })
  departmentRoles: DepartmentUserRoles[];
}
