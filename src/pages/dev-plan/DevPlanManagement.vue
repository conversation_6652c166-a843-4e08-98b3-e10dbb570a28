<template>
  <q-page padding class="q-gutter-y-lg">
    <TopHeaderTable
      title="จัดการแผนพัฒนาบุคลากร"
      create-button-label="เพิ่ม"
      @search="onSearchUpdate"
      @create="onClickAdd"
    />

    <q-table
      :rows="rows"
      :columns="IDP_DEVELOPMENT_PLANSManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn dense unelevated class="view-icon" icon="build" @click="onClickPlanning(row)" />
            <q-btn dense unelevated class="view-icon" icon="visibility" />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
      <template v-slot:body-cell-is_active="{ row }">
        <q-td class="text-center">
          <StatusCapsule :published="row.isActive" />
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { IDP_DEVELOPMENT_PLANSManagementColumns } from 'src/data/table_columns';
import type { DevelopmentPlan } from 'src/types/idp';
import { defineAsyncComponent, ref } from 'vue';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';

const $q = useQuasar();
const router = useRouter();
const onClickPlanning = (row: DevelopmentPlan) => {
  void router.push({ name: 'dev-plan-planning', params: { id: row.id } });
};

const onClickEdit = (row: DevelopmentPlan) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/idp/IdpForm.vue')),
    componentProps: {
      title: 'แก้ไขแผน',
      formData: row,
    },
    persistent: true,
  })
    .onOk((data: DevelopmentPlan) => {
      // Logic to handle updating the DevelopmentPlan
      console.log('Updated DevelopmentPlan data:', data);
      // Find and update the row in the table
      const index = rows.value.findIndex((item) => item.id === data.id);
      if (index !== -1) {
        rows.value[index] = { ...data };
      }
    })
    .onCancel(() => {
      console.log('Edit dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Edit dialog dismissed');
    });
};

const onClickDelete = (row: DevelopmentPlan) => {
  // Logic to handle delete action
  console.log('Delete row:', row);
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/idp/IdpForm.vue')),
    componentProps: {
      title: 'สร้างแผนใหม่',
    },
    persistent: true,
  })
    .onOk((data: DevelopmentPlan) => {
      // Logic to handle saving the new DevelopmentPlan
      console.log('New DevelopmentPlan data:', data);
      // Add new DevelopmentPlan to the table
      const newId = Math.max(...rows.value.map((r) => r.id)) + 1;
      rows.value.push({ ...data, id: newId });
    })
    .onCancel(() => {
      console.log('Add dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Add dialog dismissed');
    });
};

// Mock data for competencies
const mockCompetencies: DevelopmentPlan[] = [
  {
    id: 1,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2567',
    description: 'ความสามารถในการวางแผน ดำเนินการ และควบคุมโครงการให้สำเร็จตามเป้าหมาย',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 2,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2568',
    description: 'ทักษะในการสื่อสาร การนำเสนอข้อมูล และการประสานงานกับผู้อื่น',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 3,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2569',
    description: 'ความสามารถในการวิเคราะห์ปัญหา ประมวลผลข้อมูล และหาแนวทางแก้ไข',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 4,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2570',
    description: 'ความสามารถในการเป็นผู้นำ การมอบหมายงาน และการสร้างแรงบันดาลใจให้ทีม',
    isActive: false,
    originalPlan: null,
  },
  {
    id: 5,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2571',
    description: 'ทักษะการใช้เทคโนโลยี การใช้งานซอฟต์แวร์ และการปรับตัวกับเทคโนโลยีใหม่',
    isActive: false,
    originalPlan: null,
  },
];

const rows = ref<DevelopmentPlan[]>(mockCompetencies);

const onSearchUpdate = (keyword: string) => {
  // Filter rows based on search keyword
  if (!keyword) {
    rows.value = mockCompetencies;
  } else {
    rows.value = mockCompetencies.filter(
      (item) =>
        item.name?.toLowerCase().includes(keyword.toLowerCase()) ||
        item.description?.toLowerCase().includes(keyword.toLowerCase()),
    );
  }
};
</script>

<style scoped></style>
